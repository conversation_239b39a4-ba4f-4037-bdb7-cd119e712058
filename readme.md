# Sux 网页文字开发描述

## 色彩规划

整体采用绿色系列作为核心色调，构建清新自然的视觉氛围。

### 主色调
- **草木绿** (`#4CAF50`)：传递生机与活力，象征品牌的可持续理念

### 辅助色
- **浅薄荷绿** (`#81C784`)：适用于次要说明文字
- **深橄榄绿** (`#388E3C`)：强化重要标题与按钮文字

通过这三种绿色的搭配，形成和谐且有层次的色彩体系，用于文字层级区分与交互元素。

## 文字排版（简约风格）

### 字体选择
采用无衬线字体 **Inter**，其简洁的线条与清晰的字形契合简约风格，确保在不同设备上的可读性。

### 标题设计
- **字号**：2.5rem
- **字重**：700
- **行高**：1.2
- **颜色**：深橄榄绿 (`#388E3C`)
- **布局**：居中排布在页面顶部
- **风格**：减少装饰元素，仅通过字体本身的力量感突出主题

### 正文内容
- **字号**：1rem
- **字重**：400
- **行高**：1.6
- **颜色**：草木绿 (`#4CAF50`)
- **段落间距**：1.5rem
- **特点**：避免文字拥挤，提升阅读舒适度

### 辅助文字
- **字号**：0.875rem
- **字重**：300
- **颜色**：浅薄荷绿 (`#81C784`)
- **用途**：标注、说明等次要信息，与主正文形成明确区分

## 动画效果

### 文字加载动画
- **效果**：文字从透明状态逐渐过渡到正常色彩
- **位移**：轻微的向上位移（约 5px）
- **时长**：0.5s
- **目的**：营造轻盈的入场感

### 滚动交互动画
- **触发**：页面滚动至特定文字区域时
- **效果**：文字以渐显方式呈现，配合微小的缩放效果（从 0.95 倍放大至 1 倍）
- **时长**：0.3s
- **目的**：增强用户与页面的互动感

### 悬停效果
- **适用对象**：可点击的文字（如链接、按钮文字）
- **颜色变化**：从主色调草木绿渐变为深橄榄绿
- **下划线动画**：从文字中间向两端延展
- **时长**：0.3s
- **目的**：提升交互反馈的细腻度

### 数字变化动画
- **适用对象**：数据类文字（如统计数字）
- **效果**：从 0 到目标数值的平滑递增动画
- **时长**：1s
- **附加效果**：配合绿色数值的渐变变化
- **目的**：让数据展示更具动态感